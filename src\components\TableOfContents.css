/* src/components/TableOfContents.css */
.table-of-contents {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-of-contents h3 {
  color: #32CD32; /* Bright green */
  margin-bottom: 1rem;
  font-size: 1.3rem;
  border-bottom: 2px solid #32CD32;
  padding-bottom: 0.5rem;
}

.table-of-contents ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.table-of-contents li {
  margin-bottom: 0.8rem;
  color: #333; /* Dark gray for better readability */
}

.table-of-contents a {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  display: block;
  padding: 0.5rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.table-of-contents a:hover {
  background-color: rgba(50, 205, 50, 0.1); /* Light green background on hover */
  color: #32CD32; /* Bright green */
  padding-left: 0.8rem;
}

/* Responsive styles */
@media (max-width: 768px) {
  .table-of-contents {
    padding: 1rem;
  }

  .table-of-contents h3 {
    font-size: 1.1rem;
  }

  .table-of-contents a {
    font-size: 0.9rem;
  }
}
