/* src/pages/OptimalFormGuidePage.css */
.guide-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  position: relative;
  z-index: 1;
}

.guide-main-content {
  margin-top: 2rem;
}

.user-instructions, .disclaimer-box {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border-left: 4px solid #32CD32; /* Bright green */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.disclaimer-box {
  border-left-color: #FFA500; /* Orange for disclaimer */
  background-color: rgba(255, 250, 240, 0.9); /* Light orange tint */
}

.user-instructions h3, .disclaimer-box h3 {
  color: #32CD32;
  margin-top: 0;
  margin-bottom: 1rem;
}

.disclaimer-box h3 {
  color: #FFA500; /* Orange for disclaimer heading */
}

.user-instructions ul {
  margin-bottom: 0;
}

.guide-footer {
  text-align: center;
  margin-top: 3rem;
  padding: 2rem 0;
}

/* Specific styling for the optimal form guide */
.guide-section-content h3 {
  color: #32CD32; /* Bright green */
  margin-top: 2rem;
  font-size: 1.5rem;
}

.guide-section-content h4 {
  color: #444;
  margin-top: 1.2rem;
  font-size: 1.2rem;
  font-weight: 600;
}

.guide-section-content ul {
  padding-left: 1.5rem;
  margin-bottom: 1.5rem;
}

.guide-section-content ul li {
  margin-bottom: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .guide-container {
    padding: 1rem;
  }
  
  .guide-section-content h3 {
    font-size: 1.3rem;
  }
  
  .guide-section-content h4 {
    font-size: 1.1rem;
  }
}
