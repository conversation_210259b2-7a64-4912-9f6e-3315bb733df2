/* src/pages/RunnersNutritionGuidePage.css */
/* Specific styles for the Nutrition Guide page */
.nutrition-guide-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  position: relative;
  z-index: 1;
  color: #333; /* Override the global white text color */
}

.guide-main-content {
  margin-top: 2rem;
}

/* Ensure we don't override the global heading styles for other guides */
/* These styles only apply to the nutrition guide */

.user-instructions, .disclaimer-box {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border-left: 4px solid #32CD32; /* Bright green */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.disclaimer-box {
  border-left-color: #FFA500; /* Orange for disclaimer */
  background-color: rgba(255, 250, 240, 0.9); /* Light orange tint */
}

.user-instructions h3, .disclaimer-box h3 {
  color: #32CD32;
  margin-top: 0;
  margin-bottom: 1rem;
}

.disclaimer-box h3 {
  color: #FFA500; /* Orange for disclaimer heading */
}

/* Nutrition table styling */
.nutrition-table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  background-color: rgba(255, 255, 255, 0.8);
}

.nutrition-table th,
.nutrition-table td {
  padding: 1rem;
  text-align: left;
  border: 1px solid #ddd;
}

.nutrition-table th {
  background-color: rgba(50, 205, 50, 0.1);
  color: #333;
  font-weight: 600;
}

.nutrition-table tr:nth-child(even) {
  background-color: rgba(50, 205, 50, 0.05);
}

/* Meal plan styling */
.meal-plan {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  border-left: 4px solid #32CD32;
}

.meal-plan h4 {
  color: #32CD32;
  margin-top: 0;
  margin-bottom: 1rem;
}

/* Responsive styles */
@media (max-width: 768px) {
  .guide-container {
    padding: 1rem;
  }

  .user-instructions, .disclaimer-box {
    padding: 1rem;
  }

  .nutrition-table th,
  .nutrition-table td {
    padding: 0.75rem;
    font-size: 0.9rem;
  }
}

/* Print styles */
@media print {
  .guide-container::before {
    display: none;
  }

  .guide-container {
    padding: 0;
  }

  .guide-header, .table-of-contents, .guide-section {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  .guide-section {
    break-before: page;
    page-break-before: always;
  }

  .guide-section:first-of-type {
    break-before: auto;
    page-break-before: auto;
  }

  .nutrition-table {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  .meal-plan {
    break-inside: avoid;
    page-break-inside: avoid;
  }
}
