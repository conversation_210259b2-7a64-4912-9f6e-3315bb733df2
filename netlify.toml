[build]
  publish = "dist"
  command = "npm run build"

# Domain redirects for canonical URL - optimized to prevent chains
# Order matters: most specific first, then general patterns

# Redirect www to non-www with HTTPS (handles both HTTP and HTTPS www)
[[redirects]]
  from = "https://www.alt.run/*"
  to = "https://alt.run/:splat"
  status = 301
  force = true

[[redirects]]
  from = "http://www.alt.run/*"
  to = "https://alt.run/:splat"
  status = 301
  force = true

# Redirect HTTP to HTTPS (only for non-www, since www is handled above)
[[redirects]]
  from = "http://alt.run/*"
  to = "https://alt.run/:splat"
  status = 301
  force = true

# Redirect obsolete blog URL to homepage
[[redirects]]
  from = "/blog"
  to = "/"
  status = 301
  force = true

# Redirect obsolete blog URL with trailing slash to homepage
[[redirects]]
  from = "/blog/*"
  to = "/"
  status = 301
  force = true

# Redirect /events to homepage (alias)
[[redirects]]
  from = "/events"
  to = "/"
  status = 301
  force = true

# Redirect /events/ to homepage (alias with trailing slash)
[[redirects]]
  from = "/events/"
  to = "/"
  status = 301
  force = true

# Trailing slash handling: Let Netlify's "Pretty URLs" feature handle this automatically
# Netlify will serve content pages with trailing slashes and redirect non-trailing to trailing
# This eliminates the need for manual redirects and prevents conflicts

# Specific redirect for running plans with dynamic IDs
[[redirects]]
  from = "/running-plans/plan/*"
  to = "/index.html"
  status = 200

# Redirect all other routes to index.html for SPA client-side routing
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# PDF headers removed - will be added back when PDF is manually created
# [[headers]]
#   for = "/alt-run-beginner-guide.pdf"
#   [headers.values]
#     Content-Type = "application/pdf"
#     Content-Disposition = "inline; filename=alt-run-beginner-guide.pdf"
