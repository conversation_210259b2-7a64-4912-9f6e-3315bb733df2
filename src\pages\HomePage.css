/* src/pages/HomePage.css */

.homepage-layout {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 30px;
  margin-top: 0; /* Reduced from 20px */
  width: 100%; /* Ensure the layout takes full width */
  justify-content: flex-start; /* Left-justify the content */
  padding: 0 20px 0 0; /* Remove left padding, keep right padding */
}

.homepage-layout > .location-sidebar {
  flex: 0 0 auto; /* Allow the sidebar to size based on content */
  box-sizing: border-box; /* Added to ensure padding/border are included in width calculation */
  /* Appearance styles (padding, background, border) are handled by LocationSidebar.css */
  margin-right: 30px; /* Add margin to the right */
  align-self: flex-start; /* Align to the top */
}

.homepage-main-content {
  flex: 1;
  min-width: 0;
  text-align: left;
  box-sizing: border-box; /* Added for consistency */
  padding-right: 30px; /* Add padding on the right side to match the left padding */
  /* width: calc(100% - 310px); */ /* Removed fixed width calculation */
  /* background-color: lightblue; /* TEMPORARY: Add a background to visually see its bounds */
}

.homepage-main-content > h1 {
  margin-top: 0; /* Force first heading to have no top margin */
  margin-bottom: 0.3em; /* Reduced space between heading and paragraph */
}

.homepage-main-content > p {
  margin-top: 0; /* Remove top margin from paragraph */
  margin-bottom: 1em; /* Add some space after the paragraph */
}

/* Text overlay for better readability */
.text-overlay-container {
  background-color: rgba(0, 0, 0, 0.6); /* Semi-transparent black background */
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  backdrop-filter: blur(3px); /* Add a slight blur effect to the background */
  -webkit-backdrop-filter: blur(3px); /* For Safari support */
  border: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border for better definition */
}

.text-overlay-container h1 {
  margin-top: 0;
  color: #ccff00; /* Match the learn-more button color */
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.text-overlay-container p {
  color: #ccff00; /* Match the learn-more button color */
  margin-bottom: 10px; /* Add space between paragraphs */
}

/* Remove margin from the last paragraph */
.text-overlay-container p:last-child {
  margin-bottom: 0;
}

.homepage-main-content > .find-events-button-container,
.homepage-main-content > hr,
.homepage-main-content > h2 {
  margin-left: 0; /* Ensure left alignment */
  width: 100%; /* Take full width of parent */
}

.find-events-button-container {
  margin-top: 20px;
  margin-bottom: 20px;
}

/* Responsive adjustments for mobile (stacking) */
@media (max-width: 768px) {
  .homepage-layout {
    flex-direction: column;
    gap: 20px;
    padding: 0 15px 0 0; /* Remove left padding, keep right padding */
  }

  .homepage-layout > .location-sidebar,
  .homepage-main-content {
    flex-basis: auto;
    width: 100%;
  }

  .homepage-main-content {
    width: 100%; /* Override the desktop width calculation */
    padding-right: 0; /* Remove right padding on mobile */
  }

  .homepage-layout > .location-sidebar {
    margin-bottom: 20px;
    margin-right: 0;
    width: auto; /* Auto width based on content */
    align-self: flex-start; /* Ensure left alignment */
  }
}

/* Add this to ensure cards in the main content area behave as expected */
.homepage-main-content .card {
  width: 100%;
  box-sizing: border-box;
  padding-right: 30px; /* Add padding on the right side to match the gap between sidebar and content */
}

/* Load More button styling */
.load-more-container {
  display: flex;
  justify-content: center;
  margin: 30px 0;
  width: 100%;
}

.load-more-btn {
  background-color: #ccff00; /* Match the learn-more button color */
  color: #000;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s, transform 0.2s;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.load-more-btn:hover {
  background-color: #d4ff33; /* Slightly lighter on hover */
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.load-more-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.load-more-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}


