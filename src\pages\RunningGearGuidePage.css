/* src/pages/RunningGearGuidePage.css */
.guide-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  position: relative;
  z-index: 1;
}

.guide-main-content {
  margin-top: 2rem;
}

.user-instructions, .disclaimer-box {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border-left: 4px solid #32CD32; /* Bright green */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.disclaimer-box {
  border-left-color: #FFA500; /* Orange for disclaimer */
  background-color: rgba(255, 250, 240, 0.9); /* Light orange tint */
}

.user-instructions h3, .disclaimer-box h3 {
  color: #32CD32;
  margin-top: 0;
  margin-bottom: 1rem;
}

.disclaimer-box h3 {
  color: #FFA500; /* Orange for disclaimer heading */
}

.disclaimer-box p {
  color: #333; /* Dark text color for better readability */
}

.user-instructions ul {
  margin-bottom: 0;
}

.guide-footer {
  text-align: center;
  margin-top: 3rem;
  padding: 2rem 0;
}

/* Blockquote styling */
blockquote {
  font-style: italic;
  border-left: 3px solid #32CD32;
  padding-left: 1rem;
  margin: 1.5rem 0 1.5rem 1rem;
  color: #555;
  background-color: rgba(50, 205, 50, 0.05);
  padding: 1rem;
  border-radius: 4px;
}

/* Print-specific styles */
@media print {
  .guide-container {
    padding: 0;
    margin: 0;
  }

  .guide-header h1 {
    font-size: 24px !important;
    margin-top: 0 !important;
  }

  .guide-header h2 {
    display: block !important;
    visibility: visible !important;
    color: #32CD32 !important;
    font-size: 20px !important;
    margin-bottom: 10mm !important;
  }

  .guide-header p {
    display: block !important;
    visibility: visible !important;
    color: #000 !important;
  }

  /* Make sure the table of contents is visible */
  .table-of-contents {
    display: block !important;
    visibility: visible !important;
    page-break-after: always !important;
    break-after: always !important;
  }

  /* Prevent page breaks inside these elements */
  .guide-section, .guide-header, .table-of-contents, .user-instructions, .disclaimer-box {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  /* Prevent page breaks inside paragraphs and list items */
  p, li, h2, h3, h4 {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  /* Add page breaks before main sections */
  .guide-section {
    break-before: page;
    page-break-before: always;
    margin-top: 20mm; /* Add space at the top of each section to prevent cut-off headings */
  }

  /* First section doesn't need a page break */
  .guide-section:first-of-type {
    break-before: auto;
    page-break-before: auto;
    margin-top: 10mm; /* Less margin for first section */
  }

  /* Ensure headings don't get cut off */
  h2, h3 {
    margin-top: 15mm !important;
    padding-top: 5mm !important;
  }

  /* Ensure proper spacing between elements */
  .guide-section-content {
    margin-bottom: 10mm;
  }

  /* Style blockquotes for print */
  blockquote {
    margin: 10mm 5mm;
    padding: 5mm;
    border-left: 1mm solid #32CD32;
    page-break-inside: avoid;
  }
}
