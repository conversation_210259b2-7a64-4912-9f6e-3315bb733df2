/* src/components/LocationSidebar.css */
.location-sidebar {
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.7); /* Semi-transparent black background */
  min-width: 220px;
  width: auto; /* Auto width based on content */
  max-width: 280px; /* Maximum width */
  box-sizing: border-box; /* Added for consistency */
  margin-right: 30px; /* Add margin to the right */
  align-self: flex-start; /* Align to the top */
  backdrop-filter: blur(3px); /* Add a slight blur effect to the background */
  -webkit-backdrop-filter: blur(3px); /* For Safari support */
  color: white; /* Light text for better readability */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3); /* Add shadow for depth */
}

.location-sidebar h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.2em;
  color: #fff; /* White text for better contrast */
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.5); /* Subtle glow effect */
}

.filter-group {
  margin-bottom: 15px;
}

.filter-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  font-size: 0.9em;
  color: #fff; /* White text for better contrast */
}

.filter-group select,
.filter-group button {
  width: 100%;
  padding: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  box-sizing: border-box;
  font-size: 0.9em;
  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent dark background */
  color: white; /* White text */
}

.filter-group select:disabled {
  background-color: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}

.filter-group button {
  background-color: #ccff00; /* Green color matching the learn-more button */
  color: black;
  cursor: pointer;
  margin-top: 10px;
  padding: 10px;
  font-size: 1.1em;
  font-weight: bold;
  border: none;
  border-radius: 4px;
  transition: all 0.3s ease;
  box-shadow: 0 0 10px rgba(204, 255, 0, 0.5); /* Green glow */
}

.filter-group button:hover {
  background-color: #b3ff00; /* Slightly darker green on hover */
  box-shadow: 0 0 15px rgba(204, 255, 0, 0.7); /* Enhanced glow on hover */
}

/* Loading indicator styles */
.loading-indicator {
  width: 100%;
  height: 4px; /* Thin like a battery indicator */
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  margin-top: 8px;
  overflow: hidden;
  position: relative;
}

.loading-bar {
  height: 100%;
  width: 30%;
  background-color: #ccff00; /* Match the button color */
  position: absolute;
  border-radius: 2px;
  animation: loading 1.5s infinite ease-in-out;
  box-shadow: 0 0 5px rgba(204, 255, 0, 0.7); /* Subtle glow */
}

@keyframes loading {
  0% {
    left: -30%;
  }
  100% {
    left: 100%;
  }
}

/* Mobile-specific adjustments */
@media (max-width: 768px) {
  .location-sidebar {
    width: auto; /* Auto width based on content */
    min-width: 0; /* Override the min-width */
    margin: 0 0 20px 0; /* Left-align with bottom margin */
    align-self: flex-start; /* Ensure left alignment */
    box-sizing: border-box; /* Include padding in width calculation */
  }

  .filter-group {
    margin-bottom: 12px; /* Slightly reduce spacing between filter groups on mobile */
  }

  .filter-group select,
  .filter-group button {
    width: 100%; /* Keep controls full width of their container */
  }
}

/* Location controls styles */
.location-controls {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.location-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 10px;
  border-radius: 6px;
  font-size: 0.85rem;
  margin-bottom: 8px;
}

.location-status.detecting {
  background-color: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.location-status.detected {
  background-color: rgba(40, 167, 69, 0.2);
  color: #28a745;
  border: 1px solid rgba(40, 167, 69, 0.3);
}

.location-status.not-detected {
  background-color: rgba(108, 117, 125, 0.2);
  color: #6c757d;
  border: 1px solid rgba(108, 117, 125, 0.3);
}

.location-toggle-btn {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.8rem;
  transition: background-color 0.2s ease;
}

.location-toggle-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.location-controls-actions {
  margin-top: 8px;
}

.location-enable-btn {
  width: 100%;
  padding: 10px;
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.location-enable-btn:hover {
  background: linear-gradient(135deg, #218838, #1ea085);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.location-enable-btn:active {
  transform: translateY(0);
}

/* Mobile adjustments for location controls */
@media (max-width: 768px) {
  .location-controls {
    margin-top: 12px;
    padding-top: 12px;
  }

  .location-status {
    font-size: 0.8rem;
    padding: 6px 8px;
  }

  .location-enable-btn {
    padding: 8px;
    font-size: 0.85rem;
  }
}