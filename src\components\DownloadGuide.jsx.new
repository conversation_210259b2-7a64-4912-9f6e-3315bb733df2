// src/components/DownloadGuide.jsx
import React, { useState } from 'react';
import PropTypes from 'prop-types';
import html2pdf from 'html2pdf.js';
import './DownloadGuide.css';

/**
 * Component to generate and download a PDF of the guide content
 *
 * @param {Object} props - Component props
 * @param {React.RefObject} props.targetRef - Reference to the content to be converted to PDF
 * @param {string} props.buttonText - Text to display on the download button
 * @param {string} props.filename - Filename for the downloaded PDF
 */
function DownloadGuide({ targetRef, buttonText, filename = 'alt-run-beginners-guide.pdf' }) {
  const [isGenerating, setIsGenerating] = useState(false);

  const handleDownload = () => {
    if (isGenerating) return; // Prevent multiple clicks

    setIsGenerating(true);

    // Show loading state
    const button = document.querySelector('.download-button');
    if (button) {
      button.textContent = 'Preparing PDF...';
      button.disabled = true;
    }

    // Get the target element
    const element = targetRef.current;

    if (!element) {
      console.error('Target element not found');
      setIsGenerating(false);
      if (button) {
        button.textContent = buttonText;
        button.disabled = false;
      }
      return;
    }

    // Expand all FAQs for PDF
    const faqItems = document.querySelectorAll('.faq-item');
    faqItems.forEach(item => {
      if (!item.classList.contains('open')) {
        const btn = item.querySelector('.faq-question');
        if (btn) btn.click();
      }
    });

    // Basic PDF options
    const options = {
      margin: 10,
      filename: filename,
      image: { type: 'jpeg', quality: 0.95 },
      html2canvas: { scale: 1.5 },
      jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
    };

    // Use a timeout to allow DOM updates to complete
    setTimeout(() => {
      // Generate PDF directly from the element
      html2pdf()
        .from(element)
        .set(options)
        .save()
        .then(() => {
          console.log('PDF generated successfully');

          // Reset button state
          if (button) {
            button.textContent = buttonText;
            button.disabled = false;
          }

          // Reset FAQ states after a delay to ensure PDF is generated
          setTimeout(() => {
            faqItems.forEach(item => {
              if (item.classList.contains('open')) {
                const btn = item.querySelector('.faq-question');
                if (btn) btn.click();
              }
            });
            setIsGenerating(false);
          }, 1000);
        })
        .catch(error => {
          console.error('Error generating PDF:', error);

          if (button) {
            button.textContent = 'Error - Try Again';
            button.disabled = false;
          }

          // Reset FAQ states
          faqItems.forEach(item => {
            if (item.classList.contains('open')) {
              const btn = item.querySelector('.faq-question');
              if (btn) btn.click();
            }
          });

          setIsGenerating(false);
        });
    }, 500);
  };

  return (
    <div className="download-guide">
      <button
        className="download-button"
        onClick={handleDownload}
        disabled={isGenerating}
        aria-label="Download guide as PDF"
      >
        <span className="download-icon">📥</span>
        {isGenerating ? 'Preparing PDF...' : buttonText}
      </button>
    </div>
  );
}

DownloadGuide.propTypes = {
  targetRef: PropTypes.shape({
    current: PropTypes.instanceOf(Element)
  }).isRequired,
  buttonText: PropTypes.string.isRequired,
  filename: PropTypes.string
};

export default DownloadGuide;
