/* src/pages/BeginnerGuidePage.css */
.guide-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  position: relative;
  z-index: 1;
}

.guide-main-content {
  margin-top: 2rem;
}

.user-instructions, .disclaimer-box {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border-left: 4px solid #32CD32; /* Bright green */
  color: #333; /* Dark gray for better readability */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.disclaimer-box {
  border-left-color: #FFA500; /* Orange for disclaimer */
  background-color: rgba(255, 250, 240, 0.9); /* Light orange tint */
}

.user-instructions h3, .disclaimer-box h3 {
  color: #32CD32; /* Bright green */
  margin-top: 0;
  margin-bottom: 1rem;
}

.disclaimer-box h3 {
  color: #FFA500; /* Orange for disclaimer heading */
}

.user-instructions ul {
  padding-left: 1.5rem;
  margin-bottom: 0;
}

.user-instructions li {
  margin-bottom: 0.5rem;
}

.user-instructions li:last-child {
  margin-bottom: 0;
}

/* For print styling */
@media print {
  header, footer, .guide-footer {
    display: none !important;
  }

  .guide-container {
    padding: 0;
  }

  /* Make sure the guide header (title) is visible */
  .guide-header {
    display: block !important;
    visibility: visible !important;
    margin-bottom: 20mm !important;
    background-color: transparent !important;
    page-break-after: always !important;
    break-after: always !important;
  }

  .guide-header h1 {
    display: block !important;
    visibility: visible !important;
    color: #000 !important;
    font-size: 28px !important;
    font-weight: bold !important;
    margin-bottom: 10mm !important;
  }

  .guide-header h2 {
    display: block !important;
    visibility: visible !important;
    color: #32CD32 !important;
    font-size: 20px !important;
    margin-bottom: 10mm !important;
  }

  .guide-header p {
    display: block !important;
    visibility: visible !important;
    color: #000 !important;
  }

  /* Make sure the table of contents is visible */
  .table-of-contents {
    display: block !important;
    visibility: visible !important;
    page-break-after: always !important;
    break-after: always !important;
  }

  /* Prevent page breaks inside these elements */
  .guide-section, .guide-header, .table-of-contents, .user-instructions, .disclaimer-box {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  /* Prevent page breaks inside paragraphs and list items */
  p, li, h2, h3, h4 {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  /* Add page breaks before main sections */
  .guide-section {
    break-before: page;
    page-break-before: always;
    margin-top: 20mm; /* Add space at the top of each section to prevent cut-off headings */
  }

  /* First section doesn't need a page break */
  .guide-section:first-of-type {
    break-before: auto;
    page-break-before: auto;
    margin-top: 10mm; /* Less margin for first section */
  }

  /* Ensure headings don't get cut off */
  h2, h3 {
    margin-top: 15mm !important;
    padding-top: 5mm !important;
  }

  /* Ensure proper spacing between elements */
  .guide-section-content {
    margin-bottom: 10mm;
  }

  /* Ensure list items stay together with their parent lists */
  ul, ol {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  /* Special handling for FAQ items in PDF */
  .faq-item {
    margin-bottom: 20px !important;
    page-break-inside: avoid !important;
  }

  .faq-question {
    font-weight: bold !important;
    color: #32CD32 !important;
    background-color: #f9f9f9 !important;
    padding: 10px 15px !important;
    margin-bottom: 10px !important;
    border-radius: 5px !important;
    display: block !important;
  }

  /* Force display of FAQ answers in PDF */
  .faq-answer {
    display: block !important;
    padding: 10px 15px !important;
    background-color: white !important;
    border: 1px solid #eee !important;
    border-radius: 5px !important;
    margin-top: 5px !important;
    margin-bottom: 15px !important;
    visibility: visible !important;
    height: auto !important;
    opacity: 1 !important;
    overflow: visible !important;
  }

  /* Hide the expand/collapse icons in PDF */
  .faq-icon {
    display: none !important;
  }
}

/* Guide footer with logo */
.guide-footer {
  text-align: center;
  margin-top: 3rem;
  margin-bottom: 2rem;
}

.guide-footer-logo {
  height: 40px;
}

/* Download button styles removed */

/* Responsive styles */
@media (max-width: 768px) {
  .guide-container {
    padding: 1rem;
  }

  .user-instructions {
    padding: 1rem;
  }

  /* Download button styles removed */
}
