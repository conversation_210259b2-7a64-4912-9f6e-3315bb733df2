/* src/pages/NotFoundPage.css */
.not-found-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 120px);
  padding: 20px;
}

.not-found-content {
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  padding: 30px;
  max-width: 600px;
  width: 100%;
  text-align: center;
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.not-found-content h1 {
  margin-top: 0;
  color: #ccff00;
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.not-found-content p {
  font-size: 1.2rem;
  margin-bottom: 30px;
}

.not-found-links {
  text-align: left;
  margin-top: 30px;
}

.not-found-links h2 {
  color: #ccff00;
  font-size: 1.5rem;
  margin-bottom: 15px;
}

.not-found-links ul {
  list-style-type: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.not-found-links li {
  margin-bottom: 10px;
}

.not-found-links a {
  color: white;
  text-decoration: none;
  display: inline-block;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
  background-color: rgba(204, 255, 0, 0.2);
}

.not-found-links a:hover {
  background-color: rgba(204, 255, 0, 0.3);
  text-shadow: 0 0 8px #ccff00, 0 0 12px #b3ff00;
  box-shadow: 0 0 10px rgba(204, 255, 0, 0.5);
  text-decoration: none;
}

@media (max-width: 768px) {
  .not-found-links ul {
    grid-template-columns: 1fr;
  }
}
