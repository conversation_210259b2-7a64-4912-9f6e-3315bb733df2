/* src/components/forms/FormStyles.css */
.form-container {
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  padding: 2rem;
  margin: 2rem auto;
  max-width: 800px;
  color: white;
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.form-container h2 {
  color: #32CD32;
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
  text-align: center;
}

.form-description {
  text-align: center;
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.form-note {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 0.5rem;
  line-height: 1.4;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  font-size: 1rem;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="date"],
.form-group input[type="email"],
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.input-with-unit {
  display: flex;
  align-items: center;
}

.input-with-unit input {
  flex: 1;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-with-unit select {
  width: auto;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: none;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.checkbox-item {
  display: flex;
  align-items: center;
  margin-right: 1rem;
  margin-bottom: 0.5rem;
}

.checkbox-item input[type="checkbox"] {
  margin-right: 0.5rem;
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.checkbox-item label {
  margin-bottom: 0;
  cursor: pointer;
}

.toggle-item {
  display: flex;
  align-items: center;
}

.toggle-item input[type="checkbox"] {
  margin-right: 0.5rem;
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.toggle-item label {
  margin-bottom: 0;
  cursor: pointer;
}

.race-entry {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
}

.race-inputs {
  display: flex;
  flex: 1;
  gap: 0.5rem;
}

.race-inputs input {
  flex: 1;
}

.remove-button {
  background-color: #ff4d4d;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem;
  margin-left: 0.5rem;
  cursor: pointer;
  font-size: 0.8rem;
}

.remove-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.add-button {
  background-color: #4d94ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

.form-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
}

.social-share-inline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Ensure consistent sizing for social share buttons */
.social-share-inline .social-share-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: transform 0.2s, opacity 0.2s;
  overflow: hidden; /* Ensure content doesn't overflow */
}

.social-share-inline .social-share-button svg {
  width: 28px !important;
  height: 28px !important;
}

.social-share-inline .twitter-button {
  background-color: #1da1f2;
}

.social-share-inline .facebook-button {
  background-color: #4267B2;
}

.social-share-inline .reddit-button {
  background-color: #FF5700;
}

.share-label {
  margin-right: 0.5rem;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.back-button {
  background-color: #666;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.back-button:hover {
  background-color: #555;
}

.next-button {
  background-color: #32CD32;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.next-button:hover {
  background-color: #28a428;
}

/* Progress indicator */
.progress-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.progress-step {
  display: flex;
  align-items: center;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #666;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
}

.step-number.active {
  background-color: #32CD32;
}

.step-line {
  height: 2px;
  width: 30px;
  background-color: #666;
  margin: 0 0.5rem;
}

.step-line.active {
  background-color: #32CD32;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-container {
    padding: 1.5rem;
    margin: 1rem;
  }

  .checkbox-group {
    flex-direction: column;
  }

  .race-entry {
    flex-direction: column;
    align-items: flex-start;
  }

  .race-inputs {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .remove-button {
    margin-left: 0;
  }
}

.feet-inches-container {
  display: flex;
  flex: 1;
}

.feet-inches-container select {
  flex: 1;
  margin-right: 5px;
}

.feet-inches-container select:last-child {
  margin-right: 0;
}

/* DatePicker styles */
.datepicker-container {
  position: relative;
  width: 100%;
}

.datepicker-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 1rem;
  box-sizing: border-box;
  cursor: pointer;
}

.datepicker-calendar {
  background-color: white; /* Light background for better contrast */
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  color: #333; /* Dark text for better visibility */
  font-family: inherit;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.datepicker-calendar .react-datepicker__header {
  background-color: #f0f0f0; /* Light gray header */
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.datepicker-calendar .react-datepicker__current-month {
  color: #333; /* Dark text for month name */
  font-weight: bold;
}

.datepicker-calendar .react-datepicker__day-name {
  color: #555; /* Slightly lighter text for day names */
}

.datepicker-calendar .react-datepicker__day {
  color: #333; /* Dark color for all days to ensure visibility */
}

/* Current month days */
.datepicker-calendar .react-datepicker__day--in-selecting-range,
.datepicker-calendar .react-datepicker__day--in-range,
.datepicker-calendar .react-datepicker__day--selected {
  background-color: #32CD32;
  color: white;
}

/* Hover effect for all days */
.datepicker-calendar .react-datepicker__day:hover {
  background-color: #444;
  color: white;
}

/* Keyboard selected day */
.datepicker-calendar .react-datepicker__day--keyboard-selected {
  background-color: rgba(50, 205, 50, 0.7);
  color: white;
}

/* Disabled days */
.datepicker-calendar .react-datepicker__day--disabled {
  color: #666;
}

/* Fix for month navigation */
.datepicker-calendar .react-datepicker__navigation {
  top: 10px;
}

/* Fix for month dropdown */
.datepicker-calendar .react-datepicker__month-select,
.datepicker-calendar .react-datepicker__year-select {
  background-color: white;
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 5px;
  font-size: 14px;
}

/* Fix for month text */
.datepicker-calendar .react-datepicker__month-text {
  color: #333;
}

/* This rule is now redundant as we've already styled day names above */

.datepicker-popper {
  z-index: 10;
}
