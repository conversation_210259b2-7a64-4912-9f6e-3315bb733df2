/* src/components/PrintableGuide.css */
.printable-guide {
  font-family: Arial, sans-serif;
  color: #333;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: white;
}

.printable-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
  page-break-after: always;
  break-after: always;
}

.printable-logo {
  height: 60px;
  margin-bottom: 20px;
}

.printable-header h1 {
  font-size: 28px;
  margin-bottom: 10px;
  color: #000;
}

.printable-header h2 {
  font-size: 20px;
  margin-bottom: 20px;
  color: #32CD32;
  font-weight: normal;
}

.printable-date {
  font-size: 14px;
  color: #666;
  margin-top: 20px;
}

.printable-toc {
  margin-bottom: 40px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 5px;
  page-break-after: always;
}

.printable-toc h3 {
  color: #32CD32;
  margin-bottom: 15px;
  border-bottom: 2px solid #32CD32;
  padding-bottom: 5px;
}

.printable-toc ul {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.printable-toc li {
  margin-bottom: 8px;
}

.printable-toc a {
  color: #333;
  text-decoration: none;
}

.printable-section {
  margin-bottom: 40px;
  page-break-before: always;
  position: relative; /* For page number positioning */
}

.printable-section:first-of-type {
  page-break-before: auto;
}

.printable-section h2 {
  color: #32CD32;
  margin-bottom: 20px;
  font-size: 22px;
  border-bottom: 2px solid #32CD32;
  padding-bottom: 10px;
}

.printable-content {
  font-size: 14px;
}

.printable-content p {
  margin-bottom: 15px;
  page-break-inside: avoid;
}

.printable-content ul,
.printable-content ol {
  margin-bottom: 20px;
  padding-left: 20px;
  page-break-inside: avoid;
}

.printable-content li {
  margin-bottom: 8px;
  page-break-inside: avoid;
}

/* Ensure FAQ items don't break across pages */
.printable-content .faq-item {
  page-break-inside: avoid;
  break-inside: avoid;
  margin-bottom: 20px;
}

/* Ensure training plan weeks don't break across pages */
.printable-content .training-plan-week {
  page-break-inside: avoid;
  break-inside: avoid;
  margin-bottom: 15px;
}

.printable-content h3 {
  font-size: 18px;
  margin-top: 25px;
  margin-bottom: 15px;
  color: #444;
  page-break-after: avoid;
}

.printable-content h4 {
  font-size: 16px;
  margin-top: 20px;
  margin-bottom: 10px;
  color: #555;
  page-break-after: avoid;
}

.page-break {
  page-break-before: always;
  height: 0;
}

.printable-footer {
  text-align: center;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.printable-footer .printable-logo {
  height: 40px;
}

.printable-footer p {
  margin-top: 10px;
  font-size: 12px;
  color: #555;
}

.printable-disclaimer {
  font-style: italic;
  max-width: 80%;
  margin: 15px auto 0;
  font-size: 11px;
  color: #777;
}

/* We'll handle page numbers entirely through @page rules */

/* Print-specific styles */
@media print {
  @page {
    size: letter portrait;
    margin: 0.5in 0.5in 0.75in 0.5in; /* Reduced top margin */

    @bottom-center {
      content: "Page " counter(page) " of " counter(pages);
      font-family: Arial, sans-serif;
      font-size: 10pt;
    }
  }

  body {
    counter-reset: page 1 pages 100; /* Estimate total pages - will be adjusted by jsPDF */
  }

  .printable-guide {
    padding: 0;
    max-width: none;
  }

  /* Force each section to start on a new page */
  .printable-section {
    page-break-before: always;
    break-before: always;
    position: relative;
    padding-bottom: 20px;
    margin-top: 10mm; /* Add space at top to prevent cut-off headings */
  }

  /* First section doesn't need a page break */
  .printable-section:first-of-type {
    page-break-before: auto;
    break-before: auto;
  }

  /* Ensure TOC is on its own page */
  .printable-toc {
    page-break-after: always;
    break-after: always;
    page-break-before: always;
    break-before: always;
  }

  /* Ensure headings don't get orphaned */
  h2, h3, h4 {
    page-break-after: avoid;
    break-after: avoid;
  }

  /* Prevent orphaned list items */
  li:first-child {
    page-break-after: avoid;
    break-after: avoid;
  }

  li:last-child {
    page-break-before: avoid;
    break-before: avoid;
  }

  /* Ensure FAQ items stay together */
  .faq-item {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }
}
