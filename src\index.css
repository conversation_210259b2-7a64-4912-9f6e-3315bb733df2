/* src/index.css - Global Styles */

/* Basic Reset & Font */
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  overflow-x: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #000; /* Dark background for better contrast with images */
  color: #fff; /* Light text for better readability on dark backgrounds */
  line-height: 1.6;
  position: relative; /* For positioning the background container */
}

/* Consistent Link Styling */
a {
  color: #007bff; /* Example blue for links */
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
  color: #0056b3; /* Darker blue on hover */
}

/* Background Image Container */
.page-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  /* Removed fixed attachment for better mobile compatibility */
}

/* Optimize background images for different screen sizes */
@media (min-width: 769px) {
  .page-background {
    background-attachment: fixed; /* Only use fixed attachment on desktop */
  }
}

/* Mobile-specific background image optimizations */
@media (max-width: 768px) {
  .page-background {
    background-position: center center; /* Ensure image is centered */
    background-size: cover; /* Cover the entire viewport */
    height: 100vh; /* Use viewport height instead of percentage */
    width: 100vw; /* Use viewport width */
    /* Improve performance on mobile */
    will-change: transform;
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }
}

/* Hide background image when printing */
@media print {
  .page-background {
    display: none !important;
    background-image: none !important;
  }
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0.5em; /* Reduced from 1.5em */
  margin-bottom: 0.5em;
  color: #fff; /* Light color for headings on dark backgrounds */
}

h1 { font-size: 2.2em; }
h2 { font-size: 1.8em; }
h3 { font-size: 1.4em; }

/* Basic container for centering content and limiting width */
.container {
  max-width: 100%; /* Full width container */
  margin: 0;  /* No margin */
  padding: 0; /* No padding */
  width: 100%; /* Ensure it takes full width */
  box-sizing: border-box; /* Include padding in width calculation */
}

/* Utility class for cards or list items (optional) */
.card {
  background-color: rgba(0, 0, 0, 0.7); /* Semi-transparent black background */
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  backdrop-filter: blur(3px); /* Add a slight blur effect to the background */
  -webkit-backdrop-filter: blur(3px); /* For Safari support */
}

/* Button styling (example) */
button, .button-link {
  display: inline-block;
  padding: 10px 20px;
  font-size: 1em;
  font-weight: bold;
  color: #fff;
  background-color: #007bff;
  border: none;
  border-radius: 5px;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s ease-in-out;
}

button:hover, .button-link:hover {
  background-color: #0056b3;
  color: #fff;
  text-decoration: none;
}

/* Styling for lists */
ul {
  list-style-type: none; /* Remove default bullets */
  padding-left: 0;
}

ul li {
  margin-bottom: 15px; /* Space between list items */
}

hr {
  border: 0;
  height: 1px;
  background-color: #e0e0e0;
  margin: 30px 0;
}

/* Add styles to your event detail page container */
.event-detail-container {
  background-color: rgba(0, 0, 0, 0.7); /* Semi-transparent black background */
  color: white;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
  backdrop-filter: blur(3px); /* Add a slight blur effect to the background */
  -webkit-backdrop-filter: blur(3px); /* For Safari support */
  border: 1px solid rgba(255, 255, 255, 0.1); /* Subtle border for better definition */
}

.event-info p, .event-description p {
  margin-bottom: 10px;
}

.event-website-link a {
  font-weight: bold;
  display: inline-block;
  margin-top: 15px;
  padding: 8px 15px;
  background-color: #28a745; /* Green for call to action */
  color: white;
  border-radius: 5px;
}

.event-website-link a:hover {
  background-color: #218838;
  text-decoration: none;
}

.event-detail-nav {
  margin-top: 20px;
}

/* Make guide links more visible in mobile menu */
.mobile-guide-link {
  display: block;
  padding: 10px 15px;
  margin: 5px 0;
  font-weight: 500;
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.mobile-section-title {
  font-weight: bold;
  margin: 15px 0 10px;
  padding: 0 15px;
  color: #32CD32; /* Using the green color from your theme */
}

