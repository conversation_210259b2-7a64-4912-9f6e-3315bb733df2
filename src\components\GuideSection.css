/* src/components/GuideSection.css */
.guide-section {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  scroll-margin-top: 80px; /* For smooth scrolling to anchors */
}

.guide-section h2 {
  color: #32CD32; /* Bright green */
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
  border-bottom: 2px solid #32CD32;
  padding-bottom: 0.5rem;
}

.guide-section-content {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #333; /* Dark gray for better readability */
}

.guide-section-content h3 {
  color: #333;
  margin: 1.5rem 0 1rem;
  font-size: 1.4rem;
}

.guide-section-content h4 {
  color: #444;
  margin: 1.2rem 0 0.8rem;
  font-size: 1.2rem;
}

.guide-section-content p {
  margin-bottom: 1rem;
}

.guide-section-content ul,
.guide-section-content ol {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.guide-section-content li {
  margin-bottom: 0.5rem;
}

.guide-section-content strong {
  font-weight: 600;
}

.guide-section-content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 1rem 0;
}

/* Additional styles for better PDF generation */
.guide-section-content ul,
.guide-section-content ol {
  break-inside: avoid;
  page-break-inside: avoid;
}

.guide-section-content p {
  break-inside: avoid;
  page-break-inside: avoid;
  margin-bottom: 1.2rem; /* Slightly increased margin for better spacing */
}

.guide-section-content h3,
.guide-section-content h4 {
  break-after: avoid;
  page-break-after: avoid;
  margin-top: 1.8rem; /* Increased margin for better section separation */
}

/* Specific styles for PDF generation */
@media print {
  .guide-section {
    page-break-before: always;
    break-before: always;
    padding: 0;
    margin-top: 15mm; /* Add space at top to prevent cut-off headings */
    background-color: transparent !important;
    box-shadow: none !important;
  }

  /* First section doesn't need a page break */
  .guide-section:first-of-type {
    page-break-before: auto;
    break-before: auto;
  }

  .guide-section h2 {
    color: #32CD32 !important;
    font-size: 22px !important;
    margin-bottom: 15mm !important;
    padding-bottom: 5mm !important;
  }

  /* Ensure logical groups stay together */
  .guide-section-content > div,
  .guide-section-content > ul,
  .guide-section-content > ol,
  .guide-section-content > p {
    page-break-inside: avoid;
    break-inside: avoid;
  }

  /* Ensure headings stay with their content */
  .guide-section-content h3,
  .guide-section-content h4 {
    page-break-after: avoid;
    break-after: avoid;
    page-break-before: auto;
    break-before: auto;
    margin-top: 10mm !important;
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .guide-section {
    padding: 1.5rem;
  }

  .guide-section h2 {
    font-size: 1.5rem;
  }

  .guide-section-content {
    font-size: 1rem;
  }

  .guide-section-content h3 {
    font-size: 1.2rem;
  }

  .guide-section-content h4 {
    font-size: 1.1rem;
  }
}
