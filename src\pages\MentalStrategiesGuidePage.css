/* src/pages/MentalStrategiesGuidePage.css */
.guide-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  position: relative;
  z-index: 1;
}

/* Remove the background image ::before pseudo-element since we're now setting it via JavaScript */
/* .guide-container::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/images/mental-strategies-guide.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.15;
  z-index: -1;
} */

.guide-main-content {
  margin-top: 2rem;
}

.user-instructions, .disclaimer-box {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border-left: 4px solid #32CD32; /* Bright green */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.disclaimer-box {
  border-left-color: #FFA500; /* Orange for disclaimer */
  background-color: rgba(255, 250, 240, 0.9); /* Light orange tint */
}

.user-instructions h3, .disclaimer-box h3 {
  color: #32CD32;
  margin-top: 0;
  margin-bottom: 1rem;
}

.disclaimer-box h3 {
  color: #FFA500; /* Orange for disclaimer heading */
}

/* Video note styling */
.video-note {
  font-style: italic;
  color: #666;
  font-size: 0.9rem;
  margin-top: 0.5rem;
  padding-left: 1rem;
  border-left: 2px solid #32CD32;
}

/* Responsive styles */
@media (max-width: 768px) {
  .guide-container {
    padding: 1rem;
  }
  
  .user-instructions, .disclaimer-box {
    padding: 1rem;
  }
}

/* Print styles */
@media print {
  .guide-container::before {
    display: none;
  }
  
  .guide-container {
    padding: 0;
  }
  
  .guide-header, .table-of-contents, .guide-section {
    break-inside: avoid;
    page-break-inside: avoid;
  }
  
  .guide-section {
    break-before: page;
    page-break-before: always;
  }
  
  .guide-section:first-of-type {
    break-before: auto;
    page-break-before: auto;
  }
}

