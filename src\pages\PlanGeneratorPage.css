/* src/pages/PlanGeneratorPage.css */
.running-plans-page {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  color: white;
}

.running-plans-header {
  text-align: center;
  margin-bottom: 2rem;
}

.running-plans-header h1 {
  color: #32CD32;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.running-plans-header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.guide-header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.guide-logo {
  height: 40px;
  width: auto;
}

/* Loading overlay styles */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

/* Progress indicator styles */
.progress-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 1rem 0;
}

.progress-step {
  display: flex;
  align-items: center;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #666;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.step-number.active {
  background-color: #32CD32;
}

.step-line {
  height: 2px;
  width: 30px;
  background-color: #666;
  margin: 0 5px;
}

.step-line.active {
  background-color: #32CD32;
}

/* Error message styles */
.error-message {
  background-color: rgba(255, 0, 0, 0.2);
  border: 1px solid rgba(255, 0, 0, 0.5);
  border-radius: 4px;
  padding: 1rem;
  margin: 1rem 0;
  color: white;
  text-align: center;
}

/* Responsive styles */
@media (max-width: 768px) {
  .running-plans-page {
    padding: 1rem;
  }

  .running-plans-header h1 {
    font-size: 2rem;
  }

  .guide-header-top {
    flex-direction: column;
    gap: 1rem;
  }
}
