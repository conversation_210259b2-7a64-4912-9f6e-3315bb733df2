/* src/components/Footer.css */
.app-footer {
  background-color: rgba(0, 0, 0, 0.7); /* Semi-transparent black background */
  color: #f0f0f0;         /* Light text color */
  height: 60px; /* Fixed height of 60px */
  width: 100%; /* Full width */
  display: flex;
  align-items: center;
  justify-content: space-between; /* Space between the three sections */
  padding: 0 20px; /* Add horizontal padding */
  margin-top: 30px;       /* Some space above the footer */
  border-top: 1px solid rgba(255, 255, 255, 0.1); /* Subtle light border */
  box-sizing: border-box;
  backdrop-filter: blur(5px); /* Add a blur effect to the background */
  -webkit-backdrop-filter: blur(5px); /* For Safari support */
  position: relative;
  z-index: 10; /* Ensure footer is above other content */
}

/* Left section - currently empty but included for future use */
.footer-left {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

/* Center section - contains links and copyright */
.footer-center {
  flex: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

/* Right section - contains share button */
.footer-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

/* Custom styling for the share button in the footer */
.footer-right .share-button {
  padding: 6px 10px;
  font-size: 0.85rem;
}

.footer-right .share-button svg {
  width: 20px;
  height: 20px;
}

.footer-links {
  display: flex;
  justify-content: center;
  margin-bottom: 5px;
}

.footer-link {
  color: #4CAF50; /* Green color to match the site theme */
  text-decoration: none;
  margin: 0 15px;
  font-size: 0.9em;
  transition: color 0.3s ease, text-shadow 0.3s ease;
}

.footer-link:hover {
  color: #8BC34A; /* Lighter green on hover */
  text-shadow: 0 0 8px rgba(76, 175, 80, 0.6); /* Green glow effect */
}

.app-footer p {
  margin: 0;
  font-size: 0.9em;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .app-footer {
    height: auto;
    padding: 10px 20px;
    flex-direction: column;
  }

  .footer-left, .footer-center, .footer-right {
    width: 100%;
    justify-content: center;
    margin: 5px 0;
  }

  .footer-right {
    order: 2; /* Move share button above copyright on mobile */
  }

  .footer-center {
    order: 3; /* Move copyright to bottom on mobile */
  }

  .footer-links {
    flex-direction: column;
    margin-bottom: 10px;
  }

  .footer-link {
    margin: 5px 0;
  }
}