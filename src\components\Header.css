/* src/components/Header.css */
.app-header {
  background-color: rgba(0, 0, 0, 0.7); /* Semi-transparent black background */
  height: 60px; /* Fixed height of 60px */
  width: 100%; /* Full width */
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  box-sizing: border-box;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1); /* Subtle light border */
  backdrop-filter: blur(5px); /* Add a blur effect to the background */
  -webkit-backdrop-filter: blur(5px); /* For Safari support */
  position: relative;
  z-index: 100; /* Increased z-index to ensure header is above other content */
}

.app-header .header-left,
.app-header .header-right {
  flex: 0 0 20%; /* Reduce the width of left and right sections */
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0;    /* Remove any padding */
  margin: 0;     /* Remove any margin */
}

.app-header .header-center {
  flex: 0 0 60%; /* Increase the width of the center section for menu */
  display: flex;
  align-items: center;
  justify-content: center; /* Center the navigation links */
  height: 100%;
}

/* Removed duplicate rule as it's now included above */

.app-header .header-right {
  justify-content: flex-end;
}

.app-header .secondary-nav {
  display: flex;
  justify-content: flex-end;
  flex-wrap: nowrap;
  width: 100%;
}

/* Apply the same styling to secondary nav links as main nav links */
.app-header .secondary-nav .nav-link {
  margin: 0 8px; /* Same spacing as main nav */
  text-decoration: none;
  color: #FFFFFF;
  font-weight: 500;
  padding: 8px 12px; /* Same padding as main nav */
  transition: all 0.3s ease;
  font-size: 1em; /* Same font size as main nav */
  white-space: nowrap;
  border-radius: 4px;
  position: relative;
}

.app-header .secondary-nav .nav-link:hover {
  color: #FFFFFF; /* Stay white on hover */
  text-shadow: 0 0 8px #ccff00, 0 0 12px #b3ff00; /* Green glow effect matching learn-more button */
  box-shadow: 0 0 10px rgba(204, 255, 0, 0.5); /* Subtle green box glow */
  background-color: rgba(204, 255, 0, 0.15); /* Very subtle green background */
}

/* Style for active/selected secondary nav link */
.app-header .secondary-nav .nav-link.active {
  color: #FFFFFF;
  text-shadow: 0 0 8px #ccff00, 0 0 12px #b3ff00; /* Green glow effect matching learn-more button */
  box-shadow: 0 0 10px rgba(204, 255, 0, 0.5); /* Subtle green box glow */
  background-color: rgba(204, 255, 0, 0.2); /* Slightly more visible green background */
}

/* Dropdown Menu Styles */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  background: transparent;
  border: none;
  color: #FFFFFF;
  font-weight: 500;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 1em;
  white-space: nowrap;
  border-radius: 4px;
  transition: all 0.3s ease;
  position: relative;
}

.dropdown-toggle::after {
  content: '▼';
  font-size: 0.7em;
  margin-left: 6px;
  vertical-align: middle;
}

.dropdown-toggle:hover, .dropdown-toggle.active {
  color: #FFFFFF;
  text-shadow: 0 0 8px #ccff00, 0 0 12px #b3ff00; /* Green glow effect matching learn-more button */
  box-shadow: 0 0 10px rgba(204, 255, 0, 0.5); /* Subtle green box glow */
  background-color: rgba(204, 255, 0, 0.15); /* Very subtle green background */
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: rgba(0, 0, 0, 0.9);
  min-width: 220px;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  padding: 8px 0;
  z-index: 1000; /* Increased z-index to ensure it appears above all content */
  display: none;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dropdown-menu.show {
  display: block;
}

.dropdown-item, .mobile-dropdown-item {
  display: block;
  padding: 8px 16px;
  color: #FFFFFF;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 0.95em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dropdown-item:hover {
  background-color: rgba(204, 255, 0, 0.15); /* Very subtle green background */
  text-shadow: 0 0 8px #ccff00, 0 0 12px #b3ff00; /* Green glow effect matching learn-more button */
  color: #ccff00; /* Bright green color on hover */
  text-decoration: none;
}

.dropdown-item.active {
  background-color: rgba(204, 255, 0, 0.2); /* Slightly more visible green background */
  text-shadow: 0 0 8px #ccff00, 0 0 12px #b3ff00; /* Green glow effect matching learn-more button */
  color: #ccff00; /* Bright green color for active state */
}

/* Styles for the Link wrapping the logo */
.app-header .logo-link {
  display: flex; /* Helps with vertical alignment of the image */
  align-items: center;
  text-decoration: none;
  padding: 0;    /* Remove any padding */
  margin: 0;     /* Remove any margin */
}

/* Styles for the actual logo image */
.app-header .header-logo-image {
  height: 55px; /* Increased from 40px to 55px */
  width: auto;  /* Maintain aspect ratio */
  padding: 0;   /* Remove any padding */
  margin: 0;    /* Remove any margin */
}

.app-header .main-nav {
  display: flex;
  justify-content: center;
  flex-wrap: nowrap; /* Prevent wrapping */
  width: 100%; /* Take full width of parent */
}

.app-header .main-nav .nav-link {
  margin: 0 8px; /* Increased margin for more spacing */
  text-decoration: none;
  color: #FFFFFF;
  font-weight: 500;
  padding: 8px 12px; /* Increased padding */
  transition: all 0.3s ease;
  font-size: 1em; /* Increased font size from 0.85em */
  white-space: nowrap;
  border-radius: 4px;
  position: relative;
}

.app-header .main-nav .nav-link:hover {
  color: #FFFFFF; /* Stay white on hover */
  text-shadow: 0 0 8px #ccff00, 0 0 12px #b3ff00; /* Green glow effect matching learn-more button */
  box-shadow: 0 0 10px rgba(204, 255, 0, 0.5); /* Subtle green box glow */
  background-color: rgba(204, 255, 0, 0.15); /* Very subtle green background */
}

/* Style for active/selected nav link */
.app-header .main-nav .nav-link.active {
  color: #FFFFFF;
  text-shadow: 0 0 8px #ccff00, 0 0 12px #b3ff00; /* Green glow effect matching learn-more button */
  box-shadow: 0 0 10px rgba(204, 255, 0, 0.5); /* Subtle green box glow */
  background-color: rgba(204, 255, 0, 0.2); /* Slightly more visible green background */
}

/* Mobile styles for hamburger menu */
@media (max-width: 768px) {
  .app-header {
    padding: 0 15px; /* Adjust padding for mobile */
    justify-content: space-between; /* Space between logo and hamburger */
  }

  .app-header .header-left {
    flex: 0 0 auto; /* Just enough width for the logo */
  }

  /* Mobile navigation container */
  .mobile-nav-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  /* Mobile dropdown styles */
  .mobile-dropdown {
    position: relative;
    margin-right: 15px;
    z-index: 1001; /* Same as hamburger button */
  }

  /* Ensure mobile dropdown is visible on all devices */
  @media (max-width: 320px) {
    .mobile-dropdown {
      margin-right: 8px;
    }

    .mobile-dropdown-toggle {
      padding: 5px 8px;
      font-size: 0.8em;
    }
  }

  .mobile-dropdown-toggle {
    background: transparent;
    border: none;
    color: #FFFFFF;
    font-weight: 500;
    padding: 8px 12px;
    cursor: pointer;
    font-size: 0.9em;
    white-space: nowrap;
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
  }

  .mobile-dropdown-toggle::after {
    content: '▼';
    font-size: 0.7em;
    margin-left: 6px;
    vertical-align: middle;
    transition: transform 0.3s ease;
  }

  .mobile-dropdown-toggle.active::after {
    transform: rotate(180deg);
  }

  .mobile-dropdown-toggle:hover, .mobile-dropdown-toggle.active {
    color: #FFFFFF;
    text-shadow: 0 0 8px #ccff00, 0 0 12px #b3ff00; /* Green glow effect */
    box-shadow: 0 0 10px rgba(204, 255, 0, 0.5); /* Subtle green box glow */
    background-color: rgba(204, 255, 0, 0.15); /* Very subtle green background */
  }

  .mobile-dropdown-menu {
    position: absolute;
    top: 100%;
    left: auto;
    right: 0; /* Align to the right edge of its container */
    max-width: 90vw; /* Limit width to 90% of viewport width */
    width: max-content; /* Use the width of the content */
    min-width: 220px; /* Minimum width */
    background-color: rgba(0, 0, 0, 0.9);
    border-radius: 4px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    padding: 8px 0;
    z-index: 1000;
    display: none;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .mobile-dropdown-menu.show {
    display: block;
  }

  .mobile-dropdown-item {
    display: block;
    padding: 8px 16px;
    color: #FFFFFF;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.95em;
    white-space: nowrap; /* Prevent text wrapping */
    overflow: hidden;
    text-overflow: ellipsis; /* Add ellipsis for any text that might overflow */
  }

  .mobile-dropdown-item:hover {
    background-color: rgba(204, 255, 0, 0.15); /* Very subtle green background */
    text-shadow: 0 0 8px #ccff00, 0 0 12px #b3ff00; /* Green glow effect */
    color: #ccff00; /* Bright green color on hover */
    text-decoration: none;
  }

  .mobile-dropdown-item.active {
    background-color: rgba(204, 255, 0, 0.2); /* Slightly more visible green background */
    text-shadow: 0 0 8px #ccff00, 0 0 12px #b3ff00; /* Green glow effect */
    color: #ccff00; /* Bright green color for active state */
  }

  /* Hamburger button styles */
  .hamburger-button {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 22px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 1001; /* Increased z-index to be above the mobile menu */
    position: relative;
  }

  .hamburger-line {
    display: block;
    width: 100%;
    height: 3px;
    background-color: white;
    border-radius: 2px;
    transition: all 0.3s ease;
  }

  /* Hamburger animation when active */
  .hamburger-button.active .hamburger-line:nth-child(1) {
    transform: translateY(9.5px) rotate(45deg);
  }

  .hamburger-button.active .hamburger-line:nth-child(2) {
    opacity: 0;
  }

  .hamburger-button.active .hamburger-line:nth-child(3) {
    transform: translateY(-9.5px) rotate(-45deg);
  }

  /* Mobile menu dropdown */
  .mobile-menu {
    position: absolute;
    top: 60px; /* Height of the header */
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.9);
    padding: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
    z-index: 1000; /* Increased z-index to ensure it appears above all content */
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .mobile-menu.open {
    max-height: 500px; /* Adjust based on content */
    padding: 15px 0;
  }

  /* Mobile navigation styles */
  .mobile-main-nav {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }

  .mobile-main-nav .nav-link {
    margin: 8px 0;
    padding: 10px 15px;
    width: 80%;
    text-align: center;
    font-size: 1em;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
  }

  .mobile-main-nav .nav-link:hover {
    background-color: rgba(204, 255, 0, 0.15);
    text-shadow: 0 0 8px #ccff00, 0 0 12px #b3ff00;
    box-shadow: 0 0 10px rgba(204, 255, 0, 0.5);
  }

  .mobile-main-nav .nav-link.active {
    background-color: rgba(204, 255, 0, 0.2);
    text-shadow: 0 0 8px #ccff00, 0 0 12px #b3ff00;
    box-shadow: 0 0 10px rgba(204, 255, 0, 0.5);
  }

  /* Mobile secondary navigation */
  .mobile-secondary-nav {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .mobile-section-title {
    color: #ccff00; /* Bright green color */
    font-weight: 500;
    margin: 8px 0;
    width: 80%;
    text-align: center;
    font-size: 1.1em;
    padding: 5px 0;
    border-bottom: 1px solid rgba(204, 255, 0, 0.3); /* Green border */
  }

  .mobile-secondary-nav .nav-link {
    color: white;
    text-shadow: none;
    box-shadow: none;
  }

  .mobile-secondary-nav .nav-link:hover,
  .mobile-secondary-nav .nav-link.active {
    color: white;
    text-shadow: 0 0 8px #ccff00, 0 0 12px #b3ff00; /* Green glow effect matching learn-more button */
    box-shadow: 0 0 10px rgba(204, 255, 0, 0.5); /* Subtle green box glow */
    background-color: rgba(204, 255, 0, 0.15); /* Very subtle green background */
  }
}

/* For very small screens, further optimize */
@media (max-width: 480px) {
  .app-header .header-logo-image {
    height: 40px; /* Smaller logo */
  }

  .mobile-main-nav .nav-link {
    width: 90%; /* Wider links on very small screens */
    font-size: 0.95em; /* Slightly smaller font */
  }

  .mobile-dropdown-toggle {
    font-size: 0.85em; /* Smaller font for very small screens */
    padding: 6px 10px; /* Smaller padding */
  }

  .mobile-dropdown {
    margin-right: 10px; /* Less margin on very small screens */
  }

  .mobile-dropdown-item {
    font-size: 0.9em;
    padding: 8px 12px;
    white-space: normal; /* Allow text to wrap */
  }
  
  .mobile-dropdown-menu {
    min-width: 180px; /* Smaller minimum width */
  }
}
