/* src/pages/ProfessionalRunnerPage.css */
.guide-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  position: relative;
  z-index: 1;
}

.guide-main-content {
  margin-top: 2rem;
}

.user-instructions, .disclaimer-box {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border-left: 4px solid #32CD32; /* Bright green */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.disclaimer-box {
  border-left-color: #FFA500; /* Orange for disclaimer */
  background-color: rgba(255, 250, 240, 0.9); /* Light orange tint */
}

.user-instructions h3, .disclaimer-box h3 {
  color: #32CD32;
  margin-top: 0;
  margin-bottom: 1rem;
}

.disclaimer-box h3 {
  color: #FFA500; /* Orange for disclaimer heading */
}

/* Runner profile specific styles */
.runner-profile-image {
  display: block;
  max-width: 100%;
  height: auto;
  margin: 1.5rem auto;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.runner-stats {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.runner-stats h3 {
  color: #32CD32;
  margin-top: 0;
  margin-bottom: 1rem;
}

.runner-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
}

.stat-label {
  font-weight: bold;
  color: #555;
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1.1rem;
}

.achievement-list {
  margin-bottom: 1.5rem;
}

.achievement-list li {
  margin-bottom: 0.5rem;
}

/* Image placeholder styling */
.image-placeholder {
  background-color: rgba(200, 200, 200, 0.3);
  border: 2px dashed rgba(150, 150, 150, 0.5);
  border-radius: 8px;
  padding: 2rem;
  margin: 1.5rem 0;
  text-align: center;
  color: #555;
  font-style: italic;
}

/* Responsive styles */
@media (max-width: 768px) {
  .guide-container {
    padding: 1rem;
  }
  
  .user-instructions, .disclaimer-box, .runner-stats {
    padding: 1rem;
  }
  
  .runner-stats-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* Print styles */
@media print {
  .guide-container {
    padding: 0;
  }
  
  .guide-header, .table-of-contents, .guide-section {
    break-inside: avoid;
    page-break-inside: avoid;
  }
  
  .guide-section {
    break-before: page;
    page-break-before: always;
  }
  
  .guide-section:first-of-type {
    break-before: auto;
    page-break-before: auto;
  }
  
  .runner-profile-image, .image-placeholder {
    max-width: 4in;
    margin: 0.5in auto;
  }
}
