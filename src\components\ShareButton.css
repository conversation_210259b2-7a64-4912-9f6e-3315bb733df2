/* src/components/ShareButton.css */
.share-button-container {
  position: relative;
  display: inline-block;
}

.share-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #32CD32; /* Bright green to match Alt.Run theme */
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.share-button:hover {
  background-color: #28a428; /* Darker green on hover */
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.share-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.share-button svg {
  margin-right: 6px;
}

.share-text {
  display: inline-block;
  vertical-align: middle;
}

/* Dropdown styles */
.share-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 150px;
  margin-top: 8px;
  overflow: hidden;
}

.share-option {
  display: block;
  width: 100%;
  text-align: left;
  padding: 10px 15px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  color: #333;
  transition: background-color 0.2s ease;
}

.share-option:hover {
  background-color: rgba(50, 205, 50, 0.1); /* Light green background on hover */
}

.share-option:not(:last-child) {
  border-bottom: 1px solid #eee;
}

/* Responsive styles */
@media (max-width: 768px) {
  .share-button {
    padding: 6px 10px;
    font-size: 0.8rem;
  }
  
  .share-button svg {
    width: 18px;
    height: 18px;
  }
}
