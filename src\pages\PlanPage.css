/* src/pages/PlanPage.css */
.plan-page {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  color: white;
}

.plan-header {
  text-align: center;
  margin-bottom: 2rem;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  padding: 1.5rem;
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.plan-header h1 {
  color: #32CD32;
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  text-shadow: 0 0 10px rgba(50, 205, 50, 0.5);
}

.plan-header p {
  font-size: 1.2rem;
  color: white;
}

.plan-summary,
.plan-schedule,
.plan-email-section,
.plan-raw-data {
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  padding: 2rem;
  margin-bottom: 2rem;
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(3px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.disclaimer-box {
  background-color: rgba(255, 250, 240, 0.9);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border-left: 4px solid #FFA500; /* Orange for disclaimer */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.plan-summary h2,
.plan-schedule h2,
.plan-email-section h3,
.plan-raw-data h2,
.plan-email-info h3 {
  color: #32CD32;
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.8rem;
}

.disclaimer-box h3 {
  color: #FFA500; /* Orange for disclaimer heading */
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.disclaimer-box p {
  color: #333; /* Dark text color for better readability */
  line-height: 1.5;
}

.plan-phases {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.plan-phase {
  flex: 1;
  min-width: 200px;
  text-align: center;
  padding: 1.5rem;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.plan-phase h3 {
  margin-top: 0;
  color: #32CD32;
  font-size: 1.3rem;
}

.plan-phase p {
  font-size: 1.5rem;
  margin: 0;
}

.schedule-table {
  width: 100%;
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.schedule-header {
  display: flex;
  background-color: rgba(50, 205, 50, 0.2);
  font-weight: bold;
}

.schedule-row {
  display: flex;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.schedule-row:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.05);
}

.schedule-cell {
  flex: 1;
  padding: 1rem;
  min-width: 100px;
  word-wrap: break-word;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

/* Removed specific styling for first cell to allow component styling to take precedence */

.schedule-cell:last-child {
  border-right: none;
}

.plan-actions {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.button {
  background-color: #32CD32;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
  text-decoration: none;
  display: inline-block;
}

.button:hover {
  background-color: #28a428;
}

.pdf-button {
  background-color: #ff6b00; /* Orange color to make it stand out */
}

.pdf-button:hover {
  background-color: #e05e00;
}

.share-button {
  background-color: #1da1f2; /* Twitter blue color */
}

.share-button:hover {
  background-color: #0d8ecf;
}

.calendar-button {
  background-color: #4285f4; /* Google blue color */
}

.calendar-button:hover {
  background-color: #3367d6;
}

/* Social media share buttons */
.social-share-container {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1.5rem;
}

.social-share-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: transform 0.2s, opacity 0.2s;
  overflow: hidden; /* Ensure content doesn't overflow */
}

.social-share-button svg {
  width: 32px !important;
  height: 32px !important;
}

.social-share-button:hover {
  transform: scale(1.1);
  opacity: 0.9;
}

.twitter-button {
  background-color: #1da1f2;
}

.facebook-button {
  background-color: #4267B2;
}

.reddit-button {
  background-color: #FF5700;
}

.plan-error {
  text-align: center;
  background-color: rgba(255, 77, 77, 0.2);
  border: 1px solid rgba(255, 77, 77, 0.5);
  border-radius: 8px;
  padding: 2rem;
  margin: 2rem auto;
  max-width: 600px;
}

.plan-error h2 {
  color: #ff4d4d;
  margin-top: 0;
}

.plan-disclaimer {
  font-size: 0.9rem;
  line-height: 1.5;
}

.plan-disclaimer h3 {
  color: #ff6b00; /* Orange color for the disclaimer heading */
}

.plan-disclaimer p {
  color: #ff6b00; /* Orange color for the disclaimer text */
}

/* Email section styles */
.plan-email-section .form-container {
  background-color: transparent;
  border: none;
  box-shadow: none;
  padding: 0;
  margin: 0;
  max-width: 100%;
}

.plan-email-info {
  text-align: center;
  padding: 1rem;
}

.plan-email-info p {
  margin: 0.5rem 0;
}

.email-note {
  font-size: 0.9rem;
  font-style: italic;
  color: rgba(255, 255, 255, 0.8);
}

/* JSON display styles */
.plan-raw-data {
  overflow-x: auto;
}

.json-display {
  background-color: rgba(0, 0, 0, 0.5);
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
  color: #f8f8f8;
  font-family: monospace;
  font-size: 0.9rem;
  line-height: 1.4;
  max-height: 500px;
  overflow-y: auto;
}

/* Form error and success messages */
.form-error {
  color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.form-success {
  color: #32CD32;
  background-color: rgba(50, 205, 50, 0.1);
  border: 1px solid rgba(50, 205, 50, 0.3);
  padding: 0.75rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.confirmation-message {
  text-align: center;
  padding: 1rem;
  background-color: rgba(50, 205, 50, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(50, 205, 50, 0.3);
}

.confirmation-message p {
  margin: 0.75rem 0;
}

/* Print styles */
@media print {
  /* Reset all background styles */
  body, html, .plan-page, .running-plans-page, .running-plans-container {
    color: black !important;
    background-color: white !important;
    background-image: none !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  /* Hide background elements */
  .page-background {
    display: none !important;
    background-image: none !important;
  }

  /* Reset text styles */
  .plan-header h1 {
    color: black !important;
    text-shadow: none !important;
  }

  /* Reset container styles */
  .plan-summary,
  .plan-schedule,
  .disclaimer-box {
    background-color: white !important;
    box-shadow: none !important;
    border: 1px solid #ddd !important;
    break-inside: avoid !important;
    page-break-inside: avoid !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }

  .plan-summary h2,
  .plan-schedule h2 {
    color: black !important;
  }

  .disclaimer-box h3 {
    color: #FFA500 !important;
  }

  .disclaimer-box p {
    color: #333 !important;
  }

  .plan-phase {
    background-color: #f5f5f5 !important;
  }

  .plan-phase h3 {
    color: black !important;
  }

  .schedule-header {
    background-color: #f5f5f5 !important;
    display: table-header-group !important; /* Ensure header appears on each page */
  }

  /* Improve table printing */
  .schedule-table {
    display: table !important;
    width: 100% !important;
    border-collapse: collapse !important;
    page-break-inside: auto !important; /* Allow table to break across pages */
  }

  .schedule-row {
    display: table-row !important;
    page-break-inside: avoid !important; /* Avoid breaking within a row */
    break-inside: avoid !important;
  }

  .schedule-cell {
    display: table-cell !important;
    border: 1px solid #ddd !important;
    padding: 8px !important;
    vertical-align: top !important;
  }

  /* Zebra striping for better readability in print */
  .schedule-row:nth-child(even) {
    background-color: #f9f9f9 !important;
  }

  /* Hide UI elements not needed for print */
  .plan-actions,
  .plan-email-section,
  .pdf-button,
  .social-share-container,
  .social-share-inline,
  header,
  footer,
  .guide-header-top {
    display: none !important;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .plan-page {
    padding: 1rem;
  }

  .plan-header h1 {
    font-size: 2rem;
  }

  .schedule-table {
    font-size: 0.9rem;
  }

  .schedule-cell {
    padding: 0.75rem;
    min-width: 80px;
  }

  .plan-actions {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .button, .pdf-button {
    width: 100%;
    text-align: center;
    margin-bottom: 0.5rem;
  }
}
